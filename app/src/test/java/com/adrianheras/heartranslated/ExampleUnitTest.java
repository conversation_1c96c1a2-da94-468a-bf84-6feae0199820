package com.adrianheras.heartranslated;

import org.junit.Test;

import static org.junit.Assert.*;

/**
 * Unit tests for basic functionality
 */
public class ExampleUnitTest {

    @Test
    public void addition_isCorrect() {
        assertEquals(4, 2 + 2);
    }

    @Test
    public void testLanguageArrays() {
        // Test that language arrays would have expected structure
        String[] sourceLanguages = {"Polaco"};
        String[] targetLanguages = {"Español"};

        assertEquals("Should have one source language", 1, sourceLanguages.length);
        assertEquals("Should have one target language", 1, targetLanguages.length);
        assertEquals("Source language should be Polaco", "Polaco", sourceLanguages[0]);
        assertEquals("Target language should be Español", "Español", targetLanguages[0]);
    }

    @Test
    public void testStringValidation() {
        // Test string validation logic that might be used in the app
        String validText = "Dzień dobry";
        String emptyText = "";
        String whitespaceText = "   ";

        assertFalse("Valid text should not be empty", validText.trim().isEmpty());
        assertTrue("Empty text should be empty", emptyText.trim().isEmpty());
        assertTrue("Whitespace text should be empty when trimmed", whitespaceText.trim().isEmpty());
    }

    @Test
    public void testPermissionConstants() {
        // Test that permission constants are correct
        String audioPermission = "android.permission.RECORD_AUDIO";
        String internetPermission = "android.permission.INTERNET";

        assertNotNull("Audio permission should not be null", audioPermission);
        assertNotNull("Internet permission should not be null", internetPermission);
        assertTrue("Audio permission should contain RECORD_AUDIO",
                audioPermission.contains("RECORD_AUDIO"));
        assertTrue("Internet permission should contain INTERNET",
                internetPermission.contains("INTERNET"));
    }
}