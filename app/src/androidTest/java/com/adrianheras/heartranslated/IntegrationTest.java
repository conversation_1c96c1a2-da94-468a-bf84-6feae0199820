package com.adrianheras.heartranslated;

import android.Manifest;
import android.content.Context;
import android.content.pm.PackageManager;
import android.speech.SpeechRecognizer;
import android.util.Log;

import androidx.test.core.app.ActivityScenario;
import androidx.test.espresso.Espresso;
import androidx.test.ext.junit.runners.AndroidJUnit4;
import androidx.test.platform.app.InstrumentationRegistry;
import androidx.test.rule.GrantPermissionRule;

import org.junit.Before;
import org.junit.Rule;
import org.junit.Test;
import org.junit.runner.RunWith;

import static androidx.test.espresso.Espresso.onView;
import static androidx.test.espresso.action.ViewActions.click;
import static androidx.test.espresso.assertion.ViewAssertions.matches;
import static androidx.test.espresso.matcher.ViewMatchers.isDisplayed;
import static androidx.test.espresso.matcher.ViewMatchers.withId;
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertTrue;

/**
 * Integration tests for the complete translation workflow
 */
@RunWith(AndroidJUnit4.class)
public class IntegrationTest {

    private static final String TAG = "IntegrationTest";

    @Rule
    public GrantPermissionRule permissionRule = GrantPermissionRule.grant(
            Manifest.permission.RECORD_AUDIO,
            Manifest.permission.INTERNET
    );

    private Context context;

    @Before
    public void setUp() {
        context = InstrumentationRegistry.getInstrumentation().getTargetContext();
        Log.d(TAG, "Setting up integration tests");
    }

    @Test
    public void testCompleteAppFlow() {
        Log.d(TAG, "Testing complete app flow");

        try (ActivityScenario<MainActivity> scenario = ActivityScenario.launch(MainActivity.class)) {
            // Step 1: Verify app launches and UI is displayed
            onView(withId(R.id.spinnerSourceLanguage))
                    .check(matches(isDisplayed()));
            onView(withId(R.id.spinnerTargetLanguage))
                    .check(matches(isDisplayed()));
            onView(withId(R.id.switchTranslation))
                    .check(matches(isDisplayed()));
            onView(withId(R.id.switchTTS))
                    .check(matches(isDisplayed()));
            Log.d(TAG, "✓ UI components are displayed");

            // Step 2: Test TTS switch toggle
            onView(withId(R.id.switchTTS))
                    .perform(click());
            Log.d(TAG, "✓ TTS switch toggled");

            // Step 3: Test translation switch toggle
            onView(withId(R.id.switchTranslation))
                    .perform(click());
            Log.d(TAG, "✓ Translation switch toggled");

            // Wait for any initialization
            try {
                Thread.sleep(2000);
            } catch (InterruptedException e) {
                Log.e(TAG, "Sleep interrupted", e);
            }

            // Step 4: Verify permissions are working
            scenario.onActivity(activity -> {
                int audioPermission = context.checkSelfPermission(Manifest.permission.RECORD_AUDIO);
                assertEquals("Audio permission should be granted", 
                        PackageManager.PERMISSION_GRANTED, audioPermission);
                Log.d(TAG, "✓ Audio permission verified");
            });

            // Step 5: Turn off translation
            onView(withId(R.id.switchTranslation))
                    .perform(click());
            Log.d(TAG, "✓ Translation switch turned off");

            Log.d(TAG, "Complete app flow test passed");
        }
    }

    @Test
    public void testSpeechRecognitionAvailabilityIntegration() {
        Log.d(TAG, "Testing speech recognition availability integration");

        try (ActivityScenario<MainActivity> scenario = ActivityScenario.launch(MainActivity.class)) {
            scenario.onActivity(activity -> {
                boolean isAvailable = SpeechRecognizer.isRecognitionAvailable(activity);
                Log.d(TAG, "Speech recognition available: " + isAvailable);

                if (isAvailable) {
                    // If speech recognition is available, test the switch
                    Log.d(TAG, "Testing translation switch with available speech recognition");
                } else {
                    Log.w(TAG, "Speech recognition not available on this device");
                }
            });
        }
    }

    @Test
    public void testPermissionHandling() {
        Log.d(TAG, "Testing permission handling");

        // Verify that required permissions are granted
        int audioPermission = context.checkSelfPermission(Manifest.permission.RECORD_AUDIO);
        int internetPermission = context.checkSelfPermission(Manifest.permission.INTERNET);

        assertEquals("RECORD_AUDIO permission should be granted", 
                PackageManager.PERMISSION_GRANTED, audioPermission);
        assertEquals("INTERNET permission should be granted", 
                PackageManager.PERMISSION_GRANTED, internetPermission);

        Log.d(TAG, "✓ All required permissions are granted");

        try (ActivityScenario<MainActivity> scenario = ActivityScenario.launch(MainActivity.class)) {
            // Test that the app handles permissions correctly
            onView(withId(R.id.switchTranslation))
                    .perform(click());

            // Wait for permission handling
            try {
                Thread.sleep(1000);
            } catch (InterruptedException e) {
                Log.e(TAG, "Sleep interrupted", e);
            }

            Log.d(TAG, "✓ Permission handling test completed");
        }
    }

    @Test
    public void testUIStateConsistency() {
        Log.d(TAG, "Testing UI state consistency");

        try (ActivityScenario<MainActivity> scenario = ActivityScenario.launch(MainActivity.class)) {
            // Test multiple switch toggles
            for (int i = 0; i < 3; i++) {
                onView(withId(R.id.switchTranslation))
                        .perform(click());
                
                try {
                    Thread.sleep(500);
                } catch (InterruptedException e) {
                    Log.e(TAG, "Sleep interrupted", e);
                }
                
                onView(withId(R.id.switchTTS))
                        .perform(click());
                
                try {
                    Thread.sleep(500);
                } catch (InterruptedException e) {
                    Log.e(TAG, "Sleep interrupted", e);
                }
                
                Log.d(TAG, "✓ Toggle cycle " + (i + 1) + " completed");
            }

            // Verify UI is still responsive
            onView(withId(R.id.textTranslationResult))
                    .check(matches(isDisplayed()));
            
            Log.d(TAG, "✓ UI state consistency test passed");
        }
    }

    @Test
    public void testActivityLifecycle() {
        Log.d(TAG, "Testing activity lifecycle");

        try (ActivityScenario<MainActivity> scenario = ActivityScenario.launch(MainActivity.class)) {
            // Test pause and resume
            scenario.moveToState(androidx.lifecycle.Lifecycle.State.STARTED);
            Log.d(TAG, "✓ Activity moved to STARTED state");

            scenario.moveToState(androidx.lifecycle.Lifecycle.State.RESUMED);
            Log.d(TAG, "✓ Activity moved to RESUMED state");

            // Verify UI is still functional after lifecycle changes
            onView(withId(R.id.switchTranslation))
                    .check(matches(isDisplayed()));
            
            Log.d(TAG, "✓ Activity lifecycle test passed");
        }
    }

    @Test
    public void testResourceAccess() {
        Log.d(TAG, "Testing resource access");

        try (ActivityScenario<MainActivity> scenario = ActivityScenario.launch(MainActivity.class)) {
            scenario.onActivity(activity -> {
                // Test that the activity can access string resources
                String appName = activity.getString(R.string.app_name);
                String sourceLanguage = activity.getString(R.string.source_language);
                String targetLanguage = activity.getString(R.string.target_language);

                assertNotNull("App name should not be null", appName);
                assertNotNull("Source language string should not be null", sourceLanguage);
                assertNotNull("Target language string should not be null", targetLanguage);

                assertEquals("App name should match", "Hear Translated", appName);
                assertTrue("Source language should contain 'origen'", 
                        sourceLanguage.toLowerCase().contains("origen"));
                assertTrue("Target language should contain 'destino'", 
                        targetLanguage.toLowerCase().contains("destino"));

                Log.d(TAG, "✓ Resource access test passed");
            });
        }
    }

    @Test
    public void testErrorHandling() {
        Log.d(TAG, "Testing error handling scenarios");

        try (ActivityScenario<MainActivity> scenario = ActivityScenario.launch(MainActivity.class)) {
            // Test rapid switch toggling (stress test)
            for (int i = 0; i < 5; i++) {
                onView(withId(R.id.switchTranslation))
                        .perform(click());
                onView(withId(R.id.switchTranslation))
                        .perform(click());
            }

            // Verify app is still responsive
            onView(withId(R.id.textStatus))
                    .check(matches(isDisplayed()));

            Log.d(TAG, "✓ Error handling test completed");
        }
    }

    @Test
    public void testMemoryManagement() {
        Log.d(TAG, "Testing memory management");

        // Create and destroy multiple activity instances
        for (int i = 0; i < 3; i++) {
            try (ActivityScenario<MainActivity> scenario = ActivityScenario.launch(MainActivity.class)) {
                onView(withId(R.id.switchTranslation))
                        .perform(click());
                
                try {
                    Thread.sleep(1000);
                } catch (InterruptedException e) {
                    Log.e(TAG, "Sleep interrupted", e);
                }
                
                onView(withId(R.id.switchTranslation))
                        .perform(click());
                
                Log.d(TAG, "✓ Memory test cycle " + (i + 1) + " completed");
            }
        }

        Log.d(TAG, "✓ Memory management test passed");
    }
}
