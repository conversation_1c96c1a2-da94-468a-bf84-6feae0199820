package com.adrianheras.heartranslated;

import android.Manifest;
import android.content.Context;
import android.content.pm.PackageManager;
import android.util.Log;
import android.widget.Spinner;

import androidx.test.core.app.ActivityScenario;
import androidx.test.espresso.Espresso;
import androidx.test.espresso.action.ViewActions;
import androidx.test.espresso.assertion.ViewAssertions;
import androidx.test.espresso.matcher.ViewMatchers;
import androidx.test.ext.junit.runners.AndroidJUnit4;
import androidx.test.platform.app.InstrumentationRegistry;
import androidx.test.rule.GrantPermissionRule;

import org.junit.Before;
import org.junit.Rule;
import org.junit.Test;
import org.junit.runner.RunWith;

import static androidx.test.espresso.Espresso.onView;
import static androidx.test.espresso.action.ViewActions.click;
import static androidx.test.espresso.assertion.ViewAssertions.matches;
import static androidx.test.espresso.matcher.ViewMatchers.isDisplayed;
import static androidx.test.espresso.matcher.ViewMatchers.withId;
import static androidx.test.espresso.matcher.ViewMatchers.withText;
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertTrue;

/**
 * Instrumented tests for MainActivity functionality
 */
@RunWith(AndroidJUnit4.class)
public class MainActivityTest {

    private static final String TAG = "MainActivityTest";

    @Rule
    public GrantPermissionRule permissionRule = GrantPermissionRule.grant(
            Manifest.permission.RECORD_AUDIO,
            Manifest.permission.INTERNET
    );

    private Context context;

    @Before
    public void setUp() {
        context = InstrumentationRegistry.getInstrumentation().getTargetContext();
        Log.d(TAG, "Setting up test environment");
    }

    @Test
    public void testActivityLaunches() {
        Log.d(TAG, "Testing activity launch");
        
        try (ActivityScenario<MainActivity> scenario = ActivityScenario.launch(MainActivity.class)) {
            // Verify activity launches successfully
            scenario.onActivity(activity -> {
                assertNotNull("Activity should not be null", activity);
                Log.d(TAG, "Activity launched successfully");
            });
        }
    }

    @Test
    public void testUIComponentsAreDisplayed() {
        Log.d(TAG, "Testing UI components visibility");
        
        try (ActivityScenario<MainActivity> scenario = ActivityScenario.launch(MainActivity.class)) {
            // Check if all main UI components are displayed
            onView(withId(R.id.spinnerSourceLanguage))
                    .check(matches(isDisplayed()));
            Log.d(TAG, "Source language spinner is displayed");

            onView(withId(R.id.spinnerTargetLanguage))
                    .check(matches(isDisplayed()));
            Log.d(TAG, "Target language spinner is displayed");

            onView(withId(R.id.switchTranslation))
                    .check(matches(isDisplayed()));
            Log.d(TAG, "Translation switch is displayed");

            onView(withId(R.id.switchTTS))
                    .check(matches(isDisplayed()));
            Log.d(TAG, "TTS switch is displayed");

            onView(withId(R.id.textStatus))
                    .check(matches(isDisplayed()));
            Log.d(TAG, "Status text is displayed");

            onView(withId(R.id.textTranslationResult))
                    .check(matches(isDisplayed()));
            Log.d(TAG, "Translation result text is displayed");
        }
    }

    @Test
    public void testInitialTextContent() {
        Log.d(TAG, "Testing initial text content");
        
        try (ActivityScenario<MainActivity> scenario = ActivityScenario.launch(MainActivity.class)) {
            // Check initial translation result text
            onView(withId(R.id.textTranslationResult))
                    .check(matches(withText(R.string.translation_result)));
            Log.d(TAG, "Initial translation result text is correct");

            // Check that status text is initially empty
            onView(withId(R.id.textStatus))
                    .check(matches(withText("")));
            Log.d(TAG, "Initial status text is empty");
        }
    }

    @Test
    public void testTranslationSwitchToggle() {
        Log.d(TAG, "Testing translation switch toggle");
        
        try (ActivityScenario<MainActivity> scenario = ActivityScenario.launch(MainActivity.class)) {
            // Toggle translation switch on
            onView(withId(R.id.switchTranslation))
                    .perform(click());
            Log.d(TAG, "Translation switch toggled ON");

            // Wait a moment for the switch to process
            try {
                Thread.sleep(1000);
            } catch (InterruptedException e) {
                Log.e(TAG, "Sleep interrupted", e);
            }

            // Check if status text shows listening (if permissions are granted)
            scenario.onActivity(activity -> {
                if (context.checkSelfPermission(Manifest.permission.RECORD_AUDIO) 
                        == PackageManager.PERMISSION_GRANTED) {
                    Log.d(TAG, "Audio permission granted, should be listening");
                } else {
                    Log.d(TAG, "Audio permission not granted");
                }
            });

            // Toggle translation switch off
            onView(withId(R.id.switchTranslation))
                    .perform(click());
            Log.d(TAG, "Translation switch toggled OFF");
        }
    }

    @Test
    public void testTTSSwitchToggle() {
        Log.d(TAG, "Testing TTS switch toggle");
        
        try (ActivityScenario<MainActivity> scenario = ActivityScenario.launch(MainActivity.class)) {
            // Toggle TTS switch on
            onView(withId(R.id.switchTTS))
                    .perform(click());
            Log.d(TAG, "TTS switch toggled ON");

            // Toggle TTS switch off
            onView(withId(R.id.switchTTS))
                    .perform(click());
            Log.d(TAG, "TTS switch toggled OFF");
        }
    }

    @Test
    public void testPermissionsAreGranted() {
        Log.d(TAG, "Testing permissions");
        
        // Check if RECORD_AUDIO permission is granted
        int audioPermission = context.checkSelfPermission(Manifest.permission.RECORD_AUDIO);
        assertEquals("RECORD_AUDIO permission should be granted", 
                PackageManager.PERMISSION_GRANTED, audioPermission);
        Log.d(TAG, "RECORD_AUDIO permission is granted");

        // Check if INTERNET permission is granted
        int internetPermission = context.checkSelfPermission(Manifest.permission.INTERNET);
        assertEquals("INTERNET permission should be granted", 
                PackageManager.PERMISSION_GRANTED, internetPermission);
        Log.d(TAG, "INTERNET permission is granted");
    }

    @Test
    public void testSpinnerDefaultSelections() {
        Log.d(TAG, "Testing spinner default selections");
        
        try (ActivityScenario<MainActivity> scenario = ActivityScenario.launch(MainActivity.class)) {
            scenario.onActivity(activity -> {
                // Check default selections (should be first items)
                Spinner sourceSpinner = (Spinner) activity.findViewById(R.id.spinnerSourceLanguage);
                Spinner targetSpinner = (Spinner) activity.findViewById(R.id.spinnerTargetLanguage);

                assertEquals("Source language should default to first item",
                        0, sourceSpinner.getSelectedItemPosition());
                Log.d(TAG, "Source language spinner default selection is correct");

                assertEquals("Target language should default to first item",
                        0, targetSpinner.getSelectedItemPosition());
                Log.d(TAG, "Target language spinner default selection is correct");
            });
        }
    }

    @Test
    public void testSpeechRecognitionAvailability() {
        Log.d(TAG, "Testing speech recognition availability");
        
        try (ActivityScenario<MainActivity> scenario = ActivityScenario.launch(MainActivity.class)) {
            scenario.onActivity(activity -> {
                // Check if speech recognition is available on the device
                boolean isAvailable = android.speech.SpeechRecognizer.isRecognitionAvailable(activity);
                Log.d(TAG, "Speech recognition available: " + isAvailable);
                
                // This test just logs the availability, as it depends on the device
                // In a real scenario, you might want to mock this or test behavior accordingly
            });
        }
    }

    @Test
    public void testActivityRecreation() {
        Log.d(TAG, "Testing activity recreation (rotation simulation)");
        
        try (ActivityScenario<MainActivity> scenario = ActivityScenario.launch(MainActivity.class)) {
            // Simulate configuration change (like rotation)
            scenario.recreate();
            Log.d(TAG, "Activity recreated successfully");

            // Verify UI components are still displayed after recreation
            onView(withId(R.id.switchTranslation))
                    .check(matches(isDisplayed()));
            Log.d(TAG, "UI components still displayed after recreation");
        }
    }

    @Test
    public void testAppContext() {
        Log.d(TAG, "Testing app context");
        
        assertEquals("Package name should match", 
                "com.adrianheras.heartranslated", context.getPackageName());
        Log.d(TAG, "App context package name is correct");
    }
}
