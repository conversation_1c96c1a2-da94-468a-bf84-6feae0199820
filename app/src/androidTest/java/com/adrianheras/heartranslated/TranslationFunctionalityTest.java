package com.adrianheras.heartranslated;

import android.content.Context;
import android.speech.tts.TextToSpeech;
import android.util.Log;

import androidx.test.core.app.ActivityScenario;
import androidx.test.ext.junit.runners.AndroidJUnit4;
import androidx.test.platform.app.InstrumentationRegistry;

import com.google.mlkit.nl.translate.TranslateLanguage;
import com.google.mlkit.nl.translate.Translation;
import com.google.mlkit.nl.translate.Translator;
import com.google.mlkit.nl.translate.TranslatorOptions;

import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;

import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;

import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertTrue;

/**
 * Tests for translation functionality using ML Kit
 */
@RunWith(AndroidJUnit4.class)
public class TranslationFunctionalityTest {

    private static final String TAG = "TranslationTest";
    private Context context;

    @Before
    public void setUp() {
        context = InstrumentationRegistry.getInstrumentation().getTargetContext();
        Log.d(TAG, "Setting up translation functionality tests");
    }

    @Test
    public void testMLKitTranslatorInitialization() {
        Log.d(TAG, "Testing ML Kit Translator initialization");

        // Create translator options for Polish to Spanish
        TranslatorOptions options = new TranslatorOptions.Builder()
                .setSourceLanguage(TranslateLanguage.POLISH)
                .setTargetLanguage(TranslateLanguage.SPANISH)
                .build();

        Translator translator = Translation.getClient(options);
        assertNotNull("Translator should not be null", translator);
        Log.d(TAG, "ML Kit Translator initialized successfully");

        translator.close();
    }

    @Test
    public void testTranslationWithSampleText() throws InterruptedException {
        Log.d(TAG, "Testing translation with sample text");

        TranslatorOptions options = new TranslatorOptions.Builder()
                .setSourceLanguage(TranslateLanguage.POLISH)
                .setTargetLanguage(TranslateLanguage.SPANISH)
                .build();

        Translator translator = Translation.getClient(options);
        
        CountDownLatch latch = new CountDownLatch(1);
        final String[] translationResult = {null};
        final boolean[] translationSuccess = {false};

        // Test with a simple Polish phrase
        String polishText = "Dzień dobry"; // "Good day" in Polish

        translator.translate(polishText)
                .addOnSuccessListener(translatedText -> {
                    translationResult[0] = translatedText;
                    translationSuccess[0] = true;
                    Log.d(TAG, "Translation successful: " + polishText + " -> " + translatedText);
                    latch.countDown();
                })
                .addOnFailureListener(exception -> {
                    Log.e(TAG, "Translation failed", exception);
                    latch.countDown();
                });

        // Wait for translation to complete (max 30 seconds)
        boolean completed = latch.await(30, TimeUnit.SECONDS);
        assertTrue("Translation should complete within 30 seconds", completed);

        if (translationSuccess[0]) {
            assertNotNull("Translation result should not be null", translationResult[0]);
            assertFalse("Translation result should not be empty", translationResult[0].trim().isEmpty());
            Log.d(TAG, "Translation test passed: " + translationResult[0]);
        } else {
            Log.w(TAG, "Translation failed - this might be due to network issues or model not downloaded");
        }

        translator.close();
    }

    @Test
    public void testTextToSpeechInitialization() throws InterruptedException {
        Log.d(TAG, "Testing Text-to-Speech initialization");

        CountDownLatch latch = new CountDownLatch(1);
        final boolean[] ttsInitialized = {false};

        TextToSpeech tts = new TextToSpeech(context, status -> {
            if (status == TextToSpeech.SUCCESS) {
                ttsInitialized[0] = true;
                Log.d(TAG, "TTS initialized successfully");
            } else {
                Log.e(TAG, "TTS initialization failed with status: " + status);
            }
            latch.countDown();
        });

        // Wait for TTS initialization (max 10 seconds)
        boolean completed = latch.await(10, TimeUnit.SECONDS);
        assertTrue("TTS should initialize within 10 seconds", completed);

        if (ttsInitialized[0]) {
            Log.d(TAG, "TTS initialization test passed");
        } else {
            Log.w(TAG, "TTS initialization failed - this might be device-specific");
        }

        tts.shutdown();
    }

    @Test
    public void testLanguageSupport() {
        Log.d(TAG, "Testing language support");

        // Test that Polish and Spanish language codes are valid
        String polishCode = TranslateLanguage.POLISH;
        String spanishCode = TranslateLanguage.SPANISH;

        assertNotNull("Polish language code should not be null", polishCode);
        assertNotNull("Spanish language code should not be null", spanishCode);
        
        Log.d(TAG, "Polish language code: " + polishCode);
        Log.d(TAG, "Spanish language code: " + spanishCode);
        
        // Verify they are different
        assertFalse("Language codes should be different", polishCode.equals(spanishCode));
        Log.d(TAG, "Language support test passed");
    }

    @Test
    public void testTranslatorOptionsCreation() {
        Log.d(TAG, "Testing TranslatorOptions creation");

        TranslatorOptions options = new TranslatorOptions.Builder()
                .setSourceLanguage(TranslateLanguage.POLISH)
                .setTargetLanguage(TranslateLanguage.SPANISH)
                .build();

        assertNotNull("TranslatorOptions should not be null", options);
        Log.d(TAG, "TranslatorOptions created successfully");
    }

    @Test
    public void testMultipleTranslations() throws InterruptedException {
        Log.d(TAG, "Testing multiple translations");

        TranslatorOptions options = new TranslatorOptions.Builder()
                .setSourceLanguage(TranslateLanguage.POLISH)
                .setTargetLanguage(TranslateLanguage.SPANISH)
                .build();

        Translator translator = Translation.getClient(options);
        
        String[] testPhrases = {
            "Tak", // Yes
            "Nie", // No
            "Dziękuję" // Thank you
        };

        CountDownLatch latch = new CountDownLatch(testPhrases.length);
        int[] successCount = {0};

        for (String phrase : testPhrases) {
            translator.translate(phrase)
                    .addOnSuccessListener(translatedText -> {
                        successCount[0]++;
                        Log.d(TAG, "Translated: " + phrase + " -> " + translatedText);
                        latch.countDown();
                    })
                    .addOnFailureListener(exception -> {
                        Log.e(TAG, "Failed to translate: " + phrase, exception);
                        latch.countDown();
                    });
        }

        // Wait for all translations to complete (max 45 seconds)
        boolean completed = latch.await(45, TimeUnit.SECONDS);
        assertTrue("All translations should complete within 45 seconds", completed);

        Log.d(TAG, "Successful translations: " + successCount[0] + "/" + testPhrases.length);
        
        translator.close();
    }

    @Test
    public void testEmptyStringTranslation() throws InterruptedException {
        Log.d(TAG, "Testing empty string translation");

        TranslatorOptions options = new TranslatorOptions.Builder()
                .setSourceLanguage(TranslateLanguage.POLISH)
                .setTargetLanguage(TranslateLanguage.SPANISH)
                .build();

        Translator translator = Translation.getClient(options);
        
        CountDownLatch latch = new CountDownLatch(1);
        final boolean[] translationAttempted = {false};

        translator.translate("")
                .addOnSuccessListener(translatedText -> {
                    translationAttempted[0] = true;
                    Log.d(TAG, "Empty string translation result: '" + translatedText + "'");
                    latch.countDown();
                })
                .addOnFailureListener(exception -> {
                    translationAttempted[0] = true;
                    Log.d(TAG, "Empty string translation failed as expected", exception);
                    latch.countDown();
                });

        // Wait for translation attempt (max 10 seconds)
        boolean completed = latch.await(10, TimeUnit.SECONDS);
        assertTrue("Translation attempt should complete within 10 seconds", completed);
        assertTrue("Translation should be attempted", translationAttempted[0]);

        translator.close();
    }

    @Test
    public void testActivityWithTranslationComponents() {
        Log.d(TAG, "Testing activity with translation components");

        try (ActivityScenario<MainActivity> scenario = ActivityScenario.launch(MainActivity.class)) {
            scenario.onActivity(activity -> {
                // Verify that the activity can access translation-related components
                assertNotNull("Activity should not be null", activity);
                
                // Check if the activity has the necessary views for translation
                assertNotNull("Source language spinner should exist", 
                        activity.findViewById(R.id.spinnerSourceLanguage));
                assertNotNull("Target language spinner should exist", 
                        activity.findViewById(R.id.spinnerTargetLanguage));
                assertNotNull("Translation switch should exist", 
                        activity.findViewById(R.id.switchTranslation));
                assertNotNull("Translation result text should exist", 
                        activity.findViewById(R.id.textTranslationResult));
                
                Log.d(TAG, "Activity translation components test passed");
            });
        }
    }
}
