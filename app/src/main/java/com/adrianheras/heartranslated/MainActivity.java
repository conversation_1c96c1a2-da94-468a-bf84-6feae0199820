package com.adrianheras.heartranslated;

import android.Manifest;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.os.Bundle;
import android.speech.RecognitionListener;
import android.speech.RecognizerIntent;
import android.speech.SpeechRecognizer;
import android.speech.tts.TextToSpeech;
import android.util.Log;
import android.widget.ArrayAdapter;
import android.widget.Spinner;
import android.widget.Switch;
import android.widget.TextView;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.appcompat.app.AppCompatActivity;
import androidx.core.app.ActivityCompat;
import androidx.core.content.ContextCompat;

import com.google.mlkit.common.model.DownloadConditions;
import com.google.mlkit.nl.translate.TranslateLanguage;
import com.google.mlkit.nl.translate.Translation;
import com.google.mlkit.nl.translate.Translator;
import com.google.mlkit.nl.translate.TranslatorOptions;

import java.util.ArrayList;
import java.util.Locale;

public class MainActivity extends AppCompatActivity implements TextToSpeech.OnInitListener {

    private static final String TAG = "MainActivity";
    private static final int PERMISSION_REQUEST_RECORD_AUDIO = 1;

    // UI Components
    private Spinner spinnerSourceLanguage;
    private Spinner spinnerTargetLanguage;
    private Switch switchTranslation;
    private Switch switchTTS;
    private TextView textStatus;
    private TextView textTranslationResult;

    // Speech Recognition
    private SpeechRecognizer speechRecognizer;
    private Intent speechRecognizerIntent;

    // Text-to-Speech
    private TextToSpeech textToSpeech;
    private boolean isTTSReady = false;

    // Translation
    private Translator translator;

    // Audio monitoring
    private long lastAudioLogTime = 0;
    private float maxAudioLevel = 0;
    private boolean isReceivingAudio = false;

    // Language arrays
    private String[] sourceLanguages = {"Polaco"};
    private String[] targetLanguages = {"Español"};
    private String[] sourceLanguageCodes = {TranslateLanguage.POLISH};
    private String[] targetLanguageCodes = {TranslateLanguage.SPANISH};

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_main);

        initializeViews();
        setupSpinners();
        setupSwitches();
        initializeServices();
        checkPermissions();
    }

    private void initializeViews() {
        spinnerSourceLanguage = findViewById(R.id.spinnerSourceLanguage);
        spinnerTargetLanguage = findViewById(R.id.spinnerTargetLanguage);
        switchTranslation = findViewById(R.id.switchTranslation);
        switchTTS = findViewById(R.id.switchTTS);
        textStatus = findViewById(R.id.textStatus);
        textTranslationResult = findViewById(R.id.textTranslationResult);
    }

    private void setupSpinners() {
        // Source language spinner
        ArrayAdapter<String> sourceAdapter = new ArrayAdapter<>(this,
                android.R.layout.simple_spinner_item, sourceLanguages);
        sourceAdapter.setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item);
        spinnerSourceLanguage.setAdapter(sourceAdapter);

        // Target language spinner
        ArrayAdapter<String> targetAdapter = new ArrayAdapter<>(this,
                android.R.layout.simple_spinner_item, targetLanguages);
        targetAdapter.setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item);
        spinnerTargetLanguage.setAdapter(targetAdapter);
    }

    private void setupSwitches() {
        switchTranslation.setOnCheckedChangeListener((buttonView, isChecked) -> {
            if (isChecked) {
                startTranslation();
            } else {
                stopTranslation();
            }
        });

        switchTTS.setOnCheckedChangeListener((buttonView, isChecked) -> {
            // TTS toggle handled in translation result
        });
    }

    private void initializeServices() {
        // Initialize Text-to-Speech
        textToSpeech = new TextToSpeech(this, this);

        // Initialize Speech Recognizer
        if (SpeechRecognizer.isRecognitionAvailable(this)) {
            speechRecognizer = SpeechRecognizer.createSpeechRecognizer(this);
            speechRecognizer.setRecognitionListener(new SpeechRecognitionListener());

            speechRecognizerIntent = new Intent(RecognizerIntent.ACTION_RECOGNIZE_SPEECH);
            speechRecognizerIntent.putExtra(RecognizerIntent.EXTRA_LANGUAGE_MODEL,
                    RecognizerIntent.LANGUAGE_MODEL_FREE_FORM);

            // Log system default language
            String systemLanguage = java.util.Locale.getDefault().toString();
            Log.i(TAG, "🌍 System default language: " + systemLanguage);

            // Usar idioma por defecto del sistema
            // speechRecognizerIntent.putExtra(RecognizerIntent.EXTRA_LANGUAGE, "es-ES"); // Comentado para usar default
            speechRecognizerIntent.putExtra(RecognizerIntent.EXTRA_MAX_RESULTS, 5);
            speechRecognizerIntent.putExtra(RecognizerIntent.EXTRA_CALLING_PACKAGE, getPackageName());
            speechRecognizerIntent.putExtra(RecognizerIntent.EXTRA_PARTIAL_RESULTS, true);
        } else {
            Toast.makeText(this, R.string.speech_not_available, Toast.LENGTH_LONG).show();
        }

        // Initialize Translator
        setupTranslator();
    }

    private void setupTranslator() {
        int sourceIndex = spinnerSourceLanguage.getSelectedItemPosition();
        int targetIndex = spinnerTargetLanguage.getSelectedItemPosition();

        TranslatorOptions options = new TranslatorOptions.Builder()
                .setSourceLanguage(sourceLanguageCodes[sourceIndex])
                .setTargetLanguage(targetLanguageCodes[targetIndex])
                .build();

        translator = Translation.getClient(options);

        // Download model if needed
        DownloadConditions conditions = new DownloadConditions.Builder()
                .requireWifi()
                .build();

        translator.downloadModelIfNeeded(conditions)
                .addOnSuccessListener(unused -> {
                    // Model downloaded successfully
                })
                .addOnFailureListener(exception -> {
                    Toast.makeText(this, R.string.translation_error, Toast.LENGTH_SHORT).show();
                });
    }

    private void checkPermissions() {
        if (ContextCompat.checkSelfPermission(this, Manifest.permission.RECORD_AUDIO)
                != PackageManager.PERMISSION_GRANTED) {
            ActivityCompat.requestPermissions(this,
                    new String[]{Manifest.permission.RECORD_AUDIO},
                    PERMISSION_REQUEST_RECORD_AUDIO);
        }
    }

    @Override
    public void onRequestPermissionsResult(int requestCode, @NonNull String[] permissions,
                                           @NonNull int[] grantResults) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults);
        if (requestCode == PERMISSION_REQUEST_RECORD_AUDIO) {
            if (grantResults.length > 0 && grantResults[0] != PackageManager.PERMISSION_GRANTED) {
                Toast.makeText(this, R.string.permission_required, Toast.LENGTH_LONG).show();
                switchTranslation.setChecked(false);
            }
        }
    }

    private void startTranslation() {
        Log.d(TAG, "startTranslation() called");
        if (ContextCompat.checkSelfPermission(this, Manifest.permission.RECORD_AUDIO)
                == PackageManager.PERMISSION_GRANTED) {
            Log.d(TAG, "Audio permission granted");
            if (speechRecognizer != null && speechRecognizerIntent != null) {
                Log.d(TAG, "Starting speech recognition...");
                textStatus.setText(R.string.listening);
                speechRecognizer.startListening(speechRecognizerIntent);
            } else {
                Log.e(TAG, "SpeechRecognizer or Intent is null");
                Toast.makeText(this, R.string.speech_not_available, Toast.LENGTH_SHORT).show();
                switchTranslation.setChecked(false);
            }
        } else {
            Log.e(TAG, "Audio permission not granted");
            switchTranslation.setChecked(false);
            Toast.makeText(this, R.string.permission_required, Toast.LENGTH_SHORT).show();
        }
    }

    private void stopTranslation() {
        textStatus.setText("");
        if (speechRecognizer != null) {
            speechRecognizer.stopListening();
        }
    }

    private void translateText(String text) {
        Log.d(TAG, "translateText() called with: " + text);
        if (translator != null && !text.trim().isEmpty()) {
            Log.d(TAG, "Starting translation...");
            translator.translate(text)
                    .addOnSuccessListener(translatedText -> {
                        Log.d(TAG, "Translation successful: " + text + " -> " + translatedText);
                        textTranslationResult.setText(translatedText);

                        // Speak translated text if TTS is enabled
                        if (switchTTS.isChecked() && isTTSReady) {
                            Log.d(TAG, "Speaking translated text");
                            textToSpeech.speak(translatedText, TextToSpeech.QUEUE_FLUSH, null, null);
                        }
                    })
                    .addOnFailureListener(exception -> {
                        Log.e(TAG, "Translation failed", exception);
                        Toast.makeText(this, R.string.translation_error, Toast.LENGTH_SHORT).show();
                    });
        } else {
            Log.w(TAG, "Translator is null or text is empty");
        }
    }

    @Override
    public void onInit(int status) {
        if (status == TextToSpeech.SUCCESS) {
            int result = textToSpeech.setLanguage(new Locale("es", "ES")); // Spanish
            if (result == TextToSpeech.LANG_MISSING_DATA || result == TextToSpeech.LANG_NOT_SUPPORTED) {
                Toast.makeText(this, R.string.tts_not_available, Toast.LENGTH_SHORT).show();
                isTTSReady = false;
            } else {
                isTTSReady = true;
            }
        } else {
            Toast.makeText(this, R.string.tts_not_available, Toast.LENGTH_SHORT).show();
            isTTSReady = false;
        }
    }

    private class SpeechRecognitionListener implements RecognitionListener {
        @Override
        public void onReadyForSpeech(Bundle params) {
            Log.i(TAG, "🎙️ READY FOR SPEECH - Microphone is active and listening");
            textStatus.setText(R.string.listening);
            // Reset audio monitoring variables
            isReceivingAudio = false;
            maxAudioLevel = -10.0f;
            lastAudioLogTime = System.currentTimeMillis();
        }

        @Override
        public void onBeginningOfSpeech() {
            Log.i(TAG, "🗣️ SPEECH DETECTED! The system recognizes you are speaking");
            textStatus.setText("Detectando habla...");
            isReceivingAudio = true;
        }

        @Override
        public void onRmsChanged(float rmsdB) {
            // Monitor audio levels and log intelligently
            long currentTime = System.currentTimeMillis();

            // Update max audio level
            if (rmsdB > maxAudioLevel) {
                maxAudioLevel = rmsdB;
            }

            // Detect if we're receiving significant audio (above -1.0 dB)
            if (rmsdB > -1.0f) {
                if (!isReceivingAudio) {
                    isReceivingAudio = true;
                    Log.i(TAG, "🎤 AUDIO DETECTED! Starting to receive audio input");
                }
            }

            // Log audio status every 2 seconds
            if (currentTime - lastAudioLogTime > 2000) {
                Log.i(TAG, String.format("🔊 Audio Status - Current: %.2f dB, Max: %.2f dB, Receiving: %s",
                    rmsdB, maxAudioLevel, isReceivingAudio ? "YES" : "NO"));
                lastAudioLogTime = currentTime;

                // Reset max level for next period
                maxAudioLevel = rmsdB;
            }

            // Detailed logging for significant audio changes
            if (rmsdB > 5.0f) {
                Log.d(TAG, "🔊 STRONG AUDIO: " + rmsdB + " dB");
            }
        }

        @Override
        public void onBufferReceived(byte[] buffer) {
            Log.d(TAG, "onBufferReceived");
        }

        @Override
        public void onEndOfSpeech() {
            Log.i(TAG, "🔇 SPEECH ENDED - Processing what was heard...");
            textStatus.setText("Procesando...");
            isReceivingAudio = false;
        }

        @Override
        public void onError(int error) {
            String errorMessage = getErrorMessage(error);
            Log.e(TAG, "Speech recognition error: " + error + " (" + errorMessage + ")");

            // Only restart for certain errors, not for NO_MATCH
            if (error != SpeechRecognizer.ERROR_NO_MATCH &&
                switchTranslation.isChecked() &&
                speechRecognizer != null &&
                speechRecognizerIntent != null) {
                Log.d(TAG, "Restarting speech recognition after error");
                speechRecognizer.startListening(speechRecognizerIntent);
            } else if (error == SpeechRecognizer.ERROR_NO_MATCH) {
                Log.w(TAG, "No speech match found, continuing to listen...");
                if (switchTranslation.isChecked() && speechRecognizer != null && speechRecognizerIntent != null) {
                    speechRecognizer.startListening(speechRecognizerIntent);
                }
            }
        }

        private String getErrorMessage(int error) {
            switch (error) {
                case SpeechRecognizer.ERROR_AUDIO: return "Audio recording error";
                case SpeechRecognizer.ERROR_CLIENT: return "Client side error";
                case SpeechRecognizer.ERROR_INSUFFICIENT_PERMISSIONS: return "Insufficient permissions";
                case SpeechRecognizer.ERROR_NETWORK: return "Network error";
                case SpeechRecognizer.ERROR_NETWORK_TIMEOUT: return "Network timeout";
                case SpeechRecognizer.ERROR_NO_MATCH: return "No match found";
                case SpeechRecognizer.ERROR_RECOGNIZER_BUSY: return "Recognition service busy";
                case SpeechRecognizer.ERROR_SERVER: return "Server error";
                case SpeechRecognizer.ERROR_SPEECH_TIMEOUT: return "No speech input";
                default: return "Unknown error";
            }
        }

        @Override
        public void onResults(Bundle results) {
            Log.d(TAG, "onResults called");
            ArrayList<String> matches = results.getStringArrayList(SpeechRecognizer.RESULTS_RECOGNITION);
            if (matches != null && !matches.isEmpty()) {
                String spokenText = matches.get(0);
                Log.d(TAG, "Speech recognized: " + spokenText);
                translateText(spokenText);
            } else {
                Log.w(TAG, "No speech results received");
            }

            // Continue listening if translation is still active
            if (switchTranslation.isChecked() && speechRecognizer != null && speechRecognizerIntent != null) {
                Log.d(TAG, "Continuing to listen...");
                speechRecognizer.startListening(speechRecognizerIntent);
            }
        }

        @Override
        public void onPartialResults(Bundle partialResults) {
            Log.d(TAG, "onPartialResults called");
            ArrayList<String> matches = partialResults.getStringArrayList(SpeechRecognizer.RESULTS_RECOGNITION);
            if (matches != null && !matches.isEmpty()) {
                String partialText = matches.get(0);
                Log.d(TAG, "Partial speech: " + partialText);
                translateText(partialText);
            }
        }

        @Override
        public void onEvent(int eventType, Bundle params) {
            // Not used
        }
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        if (speechRecognizer != null) {
            speechRecognizer.destroy();
        }
        if (textToSpeech != null) {
            textToSpeech.stop();
            textToSpeech.shutdown();
        }
        if (translator != null) {
            translator.close();
        }
    }
}
