<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:padding="16dp"
    android:paddingTop="24dp">

    <!-- Source Language Selection -->
    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/source_language"
        android:textSize="18sp"
        android:textStyle="bold"
        android:layout_marginBottom="12dp"
        android:layout_marginTop="8dp" />

    <Spinner
        android:id="@+id/spinnerSourceLanguage"
        android:layout_width="match_parent"
        android:layout_height="48dp"
        android:layout_marginBottom="20dp"
        android:background="@android:drawable/btn_dropdown"
        android:padding="12dp" />

    <!-- Target Language Selection -->
    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/target_language"
        android:textSize="18sp"
        android:textStyle="bold"
        android:layout_marginBottom="12dp" />

    <Spinner
        android:id="@+id/spinnerTargetLanguage"
        android:layout_width="match_parent"
        android:layout_height="48dp"
        android:layout_marginBottom="20dp"
        android:background="@android:drawable/btn_dropdown"
        android:padding="12dp" />

    <!-- Translation Switch -->
    <Switch
        android:id="@+id/switchTranslation"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/translation_switch"
        android:textSize="18sp"
        android:layout_marginBottom="20dp"
        android:padding="8dp" />

    <!-- Text-to-Speech Switch -->
    <Switch
        android:id="@+id/switchTTS"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/tts_switch"
        android:textSize="18sp"
        android:layout_marginBottom="20dp"
        android:padding="8dp" />

    <!-- Status Text -->
    <TextView
        android:id="@+id/textStatus"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text=""
        android:textSize="14sp"
        android:textColor="@android:color/holo_blue_dark"
        android:layout_marginBottom="16dp"
        android:gravity="center" />

    <!-- Translation Result Box -->
    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="Traducción:"
        android:textSize="16sp"
        android:textStyle="bold"
        android:layout_marginBottom="8dp" />

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:background="@android:drawable/edit_text"
        android:padding="12dp">

        <TextView
            android:id="@+id/textTranslationResult"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="@string/translation_result"
            android:textSize="16sp"
            android:textColor="@android:color/black"
            android:minHeight="100dp" />

    </ScrollView>

</LinearLayout>
