#!/bin/bash

# Script para ejecutar todos los tests de la aplicación HearTranslated
# Asegúrate de tener un dispositivo Android conectado o un emulador ejecutándose

echo "🚀 Iniciando tests para HearTranslated..."
echo "================================================"

# Verificar que hay un dispositivo conectado
echo "📱 Verificando dispositivos conectados..."
adb devices

# Compilar la aplicación
echo ""
echo "🔨 Compilando aplicación..."
./gradlew assembleDebug

if [ $? -ne 0 ]; then
    echo "❌ Error al compilar la aplicación"
    exit 1
fi

echo "✅ Aplicación compilada exitosamente"

# Ejecutar tests unitarios
echo ""
echo "🧪 Ejecutando tests unitarios..."
./gradlew test

if [ $? -ne 0 ]; then
    echo "❌ Algunos tests unitarios fallaron"
else
    echo "✅ Tests unitarios completados"
fi

# Compilar tests instrumentados
echo ""
echo "🔨 Compilando tests instrumentados..."
./gradlew assembleAndroidTest

if [ $? -ne 0 ]; then
    echo "❌ Error al compilar tests instrumentados"
    exit 1
fi

# Instalar APK en el dispositivo
echo ""
echo "📲 Instalando aplicación en el dispositivo..."
./gradlew installDebug

if [ $? -ne 0 ]; then
    echo "❌ Error al instalar la aplicación"
    exit 1
fi

echo "✅ Aplicación instalada"

# Ejecutar tests instrumentados
echo ""
echo "🧪 Ejecutando tests instrumentados..."
echo "Nota: Estos tests requieren interacción con el dispositivo"

echo ""
echo "📋 Ejecutando MainActivityTest..."
./gradlew connectedAndroidTest -Pandroid.testInstrumentationRunnerArguments.class=com.adrianheras.heartranslated.MainActivityTest

echo ""
echo "📋 Ejecutando TranslationFunctionalityTest..."
./gradlew connectedAndroidTest -Pandroid.testInstrumentationRunnerArguments.class=com.adrianheras.heartranslated.TranslationFunctionalityTest

echo ""
echo "📋 Ejecutando IntegrationTest..."
./gradlew connectedAndroidTest -Pandroid.testInstrumentationRunnerArguments.class=com.adrianheras.heartranslated.IntegrationTest

# Ejecutar todos los tests instrumentados
echo ""
echo "📋 Ejecutando todos los tests instrumentados..."
./gradlew connectedAndroidTest

echo ""
echo "================================================"
echo "🏁 Tests completados!"
echo ""
echo "📊 Para ver los reportes detallados:"
echo "   - Tests unitarios: app/build/reports/tests/testDebugUnitTest/index.html"
echo "   - Tests instrumentados: app/build/reports/androidTests/connected/index.html"
echo ""
echo "📱 Para ver logs en tiempo real durante los tests:"
echo "   adb logcat | grep -E '(MainActivityTest|TranslationTest|IntegrationTest)'"
echo ""
echo "🔍 Para verificar permisos en el dispositivo:"
echo "   adb shell pm list permissions com.adrianheras.heartranslated"
echo ""
echo "✨ ¡Listo para probar la aplicación!"
